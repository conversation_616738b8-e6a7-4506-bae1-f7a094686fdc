import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import LoginView from '../views/LoginView.vue'
import MainLayout from '../layouts/MainLayout.vue'
import DashboardView from '../views/DashboardView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      component: MainLayout,
      meta: {
        requiresAuth: true
      },
      children: [
        {
          path: '',
          redirect: '/dashboard'
        },
        {
          path: 'dashboard',
          name: 'dashboard',
          component: DashboardView,
          meta: {
            title: '工作台',
            requiresAuth: true
          }
        },
        {
          path: 'patients',
          name: 'patients',
          component: () => import('../views/LifecycleView.vue'),
          meta: {
            title: '患者管理',
            requiresAuth: true
          }
        },
        {
          path: 'analytics',
          name: 'analytics',
          component: () => import('../views/AnalyticsView.vue'),
          meta: {
            title: '测试数据',
            requiresAuth: true
          }
        },
        {
          path: 'projects',
          name: 'projects',
          component: () => import('../views/ProjectsView.vue'),
          meta: {
            title: '报告中心',
            requiresAuth: true
          }
        },
        {
          path: 'team',
          name: 'team',
          component: () => import('../views/TeamView.vue'),
          meta: {
            title: '用户管理',
            requiresAuth: true
          }
        },
        {
          path: 'data-library',
          name: 'data-library',
          component: () => import('../views/DataLibraryView.vue'),
          meta: {
            title: '数据统计',
            requiresAuth: true
          }
        },
        {
          path: 'reports',
          name: 'reports',
          component: () => import('../views/ReportsView.vue'),
          meta: {
            title: '评分管理',
            requiresAuth: true
          }
        },
        {
          path: 'workstation',
          name: 'workstation',
          component: () => import('../views/WorkstationView.vue'),
          meta: {
            title: '医生工作站',
            requiresAuth: true
          }
        },
        {
          path: 'settings',
          name: 'settings',
          component: () => import('../views/SettingsView.vue'),
          meta: {
            title: '系统设置',
            requiresAuth: true
          }
        }
      ]
    },
    // 重定向到登录页
    {
      path: '/logout',
      redirect: '/login'
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 认知测试系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (authStore.isAuthenticated) {
      next()
    } else {
      next('/login')
    }
  } else {
    // 如果已登录且访问登录页，重定向到首页
    if (to.name === 'login' && authStore.isAuthenticated) {
      next('/')
    } else {
      next()
    }
  }
})

export default router
