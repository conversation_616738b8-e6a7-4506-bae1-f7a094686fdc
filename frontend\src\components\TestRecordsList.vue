<template>
  <div class="test-records-list">
    <!-- 测试统计信息 -->
    <div v-if="stats.completedTests > 0" class="test-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">测试次数</div>
          <div class="stat-value">{{ stats.completedTests }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">平均分数</div>
          <div class="stat-value">{{ stats.averageScore }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">最佳成绩</div>
          <div class="stat-value">{{ stats.bestScore }}</div>
        </div>
        <div class="stat-item" v-if="stats.improvement !== 0">
          <div class="stat-label">成绩变化</div>
          <div class="stat-value" :class="{ 'positive': stats.improvement > 0, 'negative': stats.improvement < 0 }">
            {{ stats.improvement > 0 ? '+' : '' }}{{ stats.improvement }}
          </div>
        </div>
      </div>
    </div>

    <!-- 测试记录列表 -->
    <div v-if="records.length > 0" class="records-list">
      <div class="records-header">
        <h5>历史测试记录</h5>
        <span class="records-count">共 {{ records.length }} 次</span>
      </div>
      
      <div class="records-table">
        <div class="table-header">
          <div class="col-date">测试日期</div>
          <div class="col-time">用时</div>
          <div class="col-score">得分</div>
          <div class="col-status">状态</div>
          <div class="col-action">操作</div>
        </div>
        
        <div class="table-body">
          <div 
            v-for="record in records" 
            :key="record.id" 
            class="table-row"
            :class="{ 'retesting': record.isRetesting }"
          >
            <div class="col-date">
              <div class="date-info">
                <div class="date-main">{{ formatDate(record.startTime) }}</div>
                <div class="date-time">{{ formatTime(record.startTime) }}</div>
              </div>
            </div>
            
            <div class="col-time">
              <div class="duration-info">
                <div class="duration-main">{{ formatDuration(record.duration || 0) }}</div>
                <div class="duration-detail" v-if="record.endTime">
                  {{ formatTime(record.startTime) }} - {{ formatTime(record.endTime) }}
                </div>
              </div>
            </div>
            
            <div class="col-score">
              <div class="score-info">
                <div class="score-main" v-if="record.score !== undefined">{{ record.score }}分</div>
                <div class="score-percentile" v-if="record.results?.overallPercentile">
                  第{{ record.results.overallPercentile }}百分位
                </div>
              </div>
            </div>
            
            <div class="col-status">
              <div class="status-badge" :class="record.status">
                {{ getStatusText(record.status) }}
              </div>
              <div class="test-type" v-if="record.isRetesting">
                <span class="retest-badge">重测</span>
              </div>
            </div>
            
            <div class="col-action">
              <button
                v-if="record.status === 'completed'"
                @click="$emit('viewResult', record)"
                class="view-result-btn"
              >
                查看结果
              </button>
              <button
                v-else-if="record.status === 'inProgress'"
                @click="$emit('continueTest', record)"
                class="continue-btn"
              >
                继续测试
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-records">
      <div class="empty-icon">📋</div>
      <p class="empty-text">暂无测试记录</p>
      <p class="empty-hint">完成测试后，记录将显示在这里</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TestRecord } from '@/types/user'
import dayjs from 'dayjs'

interface Props {
  records: TestRecord[]
  testType: string
}

interface Emits {
  (e: 'viewResult', record: TestRecord): void
  (e: 'continueTest', record: TestRecord): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算统计信息
const stats = computed(() => {
  const completedRecords = props.records.filter(record => record.status === 'completed')
  
  if (completedRecords.length === 0) {
    return {
      totalTests: 0,
      completedTests: 0,
      averageScore: 0,
      bestScore: 0,
      improvement: 0
    }
  }

  const scores = completedRecords.map(record => record.score || 0)
  const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
  const bestScore = Math.max(...scores)
  
  // 计算改善程度（最新成绩与首次成绩的差值）
  const firstScore = completedRecords[completedRecords.length - 1]?.score || 0
  const latestScore = completedRecords[0]?.score || 0
  const improvement = latestScore - firstScore

  return {
    totalTests: props.records.length,
    completedTests: completedRecords.length,
    averageScore,
    bestScore,
    improvement
  }
})

// 格式化方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MM月DD日')
}

const formatTime = (dateString: string) => {
  return dayjs(dateString).format('HH:mm')
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  if (minutes === 0) {
    return `${remainingSeconds}秒`
  }
  return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'completed': '已完成',
    'inProgress': '进行中',
    'pending': '待开始',
    'failed': '已失败'
  }
  return statusMap[status] || status
}
</script>

<style lang="scss" scoped>
.test-records-list {
  margin-top: 16px;
}

.test-stats {
  background: var(--muted);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
  }

  .stat-item {
    text-align: center;

    .stat-label {
      font-size: 12px;
      color: var(--muted-foreground);
      margin-bottom: 4px;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 600;
      color: var(--foreground);

      &.positive {
        color: var(--success);
      }

      &.negative {
        color: var(--destructive);
      }
    }
  }
}

.records-list {
  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h5 {
      font-size: 14px;
      font-weight: 600;
      color: var(--foreground);
      margin: 0;
    }

    .records-count {
      font-size: 12px;
      color: var(--muted-foreground);
    }
  }
}

.records-table {
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;

  .table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr;
    background: var(--muted);
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 600;
    color: var(--muted-foreground);
    border-bottom: 1px solid var(--border);

    @media (max-width: 768px) {
      grid-template-columns: 2fr 1fr 1fr 1fr;
      
      .col-time {
        display: none;
      }
    }
  }

  .table-body {
    .table-row {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr;
      padding: 12px 16px;
      border-bottom: 1px solid var(--border);
      transition: background-color 0.2s;

      &:hover {
        background: var(--muted);
      }

      &:last-child {
        border-bottom: none;
      }

      &.retesting {
        background: oklch(from var(--primary) l c h / 0.05);
      }

      @media (max-width: 768px) {
        grid-template-columns: 2fr 1fr 1fr 1fr;
        
        .col-time {
          display: none;
        }
      }
    }
  }
}

.date-info, .duration-info, .score-info {
  .date-main, .duration-main, .score-main {
    font-size: 14px;
    font-weight: 500;
    color: var(--foreground);
    line-height: 1.2;
  }

  .date-time, .duration-detail, .score-percentile {
    font-size: 14px;
    color: var(--muted-foreground);
    margin-top: 2px;
  }
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;

  &.completed {
    background: var(--success);
    color: white;
  }

  &.inProgress {
    background: var(--warning);
    color: white;
  }

  &.pending {
    background: var(--muted);
    color: var(--muted-foreground);
  }

  &.failed {
    background: var(--destructive);
    color: white;
  }
}

.retest-badge {
  display: inline-block;
  padding: 1px 6px;
  background: var(--primary);
  color: white;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
}

.view-result-btn, .continue-btn {
  padding: 4px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--background);
  color: var(--foreground);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--muted);
    border-color: var(--primary);
  }
}

.continue-btn {
  background: var(--primary);
  color: white;
  border-color: var(--primary);

  &:hover {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
  }
}

.empty-records {
  text-align: center;
  padding: 32px 16px;
  color: var(--muted-foreground);

  .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .empty-hint {
    font-size: 14px;
  }
}

// 平板横屏优化
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .records-table {
    .table-header, .table-row {
      grid-template-columns: 2fr 1.2fr 1fr 1fr 1.2fr;
      padding: 10px 12px;
    }
  }

  .test-stats {
    .stats-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 12px;
    }
  }
}
</style>
