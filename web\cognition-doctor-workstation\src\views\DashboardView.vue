<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-title">患者总数</span>
          <div class="stat-trend positive">
            <el-icon><TrendCharts /></el-icon>
            <span>+15.3%</span>
          </div>
        </div>
        <div class="stat-value">1,250</div>
        <div class="stat-subtitle">本月新增患者</div>
        <div class="stat-description">过去4个月的统计数据</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-title">待评分测试</span>
          <div class="stat-trend negative">
            <el-icon><Bottom /></el-icon>
            <span>-20%</span>
          </div>
        </div>
        <div class="stat-value">1,234</div>
        <div class="stat-subtitle">本期下降20%</div>
        <div class="stat-description">需要关注评分进度</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-title">活跃用户</span>
          <div class="stat-trend positive">
            <el-icon><TrendCharts /></el-icon>
            <span>+12.3%</span>
          </div>
        </div>
        <div class="stat-value">45,678</div>
        <div class="stat-subtitle">用户留存率良好</div>
        <div class="stat-description">各项指标达标</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-title">完成率</span>
          <div class="stat-trend positive">
            <el-icon><TrendCharts /></el-icon>
            <span>+4.1%</span>
          </div>
        </div>
        <div class="stat-value">4.5%</div>
        <div class="stat-subtitle">稳定表现</div>
        <div class="stat-description">符合预期目标</div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="chart-section">
      <div class="chart-header">
        <div class="chart-title">
          <h3>测试数据统计</h3>
          <p>过去3个月的数据汇总</p>
        </div>
        <div class="chart-controls">
          <el-button-group>
            <el-button :type="chartPeriod === 'last3months' ? 'primary' : 'default'" @click="chartPeriod = 'last3months'">过去3个月</el-button>
            <el-button :type="chartPeriod === 'last30days' ? 'primary' : 'default'" @click="chartPeriod = 'last30days'">过去30天</el-button>
            <el-button :type="chartPeriod === 'last7days' ? 'primary' : 'default'" @click="chartPeriod = 'last7days'">过去7天</el-button>
          </el-button-group>
        </div>
      </div>
      
      <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-tabs">
          <el-button :type="activeTab === 'outline' ? 'primary' : 'text'" @click="activeTab = 'outline'">概览</el-button>
          <el-button :type="activeTab === 'performance' ? 'primary' : 'text'" @click="activeTab = 'performance'">测试表现</el-button>
          <el-button :type="activeTab === 'key-personnel' ? 'primary' : 'text'" @click="activeTab = 'key-personnel'">关键人员</el-button>
          <el-button :type="activeTab === 'focus-documents' ? 'primary' : 'text'" @click="activeTab = 'focus-documents'">重点文档</el-button>
        </div>
        <div class="table-actions">
          <el-button>
            <el-icon><Setting /></el-icon>
            自定义列
          </el-button>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            添加部分
          </el-button>
        </div>
      </div>
      
      <el-table :data="tableData" class="data-table">
        <el-table-column prop="header" label="标题" width="200" />
        <el-table-column prop="sectionType" label="类型" width="150" />
        <el-table-column prop="target" label="目标" width="100" />
        <el-table-column prop="limit" label="限制" width="100" />
        <el-table-column prop="reviewer" label="评审员" width="150" />
        <el-table-column label="操作" width="100">
          <template #default>
            <el-button text>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import {
  TrendCharts,
  Bottom,
  Setting,
  Plus,
  MoreFilled
} from '@element-plus/icons-vue'

// 注册ECharts组件
echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// 图表相关
const chartRef = ref<HTMLElement>()
const chartPeriod = ref('last3months')
let chartInstance: echarts.ECharts | null = null

// 表格相关
const activeTab = ref('outline')
const tableData = ref([
  {
    header: '测试覆盖页面',
    sectionType: '覆盖页面',
    target: '18',
    limit: '5',
    reviewer: '张医生'
  }
])

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日', '2月5日', '2月12日', '2月19日', '2月26日', '3月5日', '3月12日', '3月19日', '3月26日'],
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280'
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6'
        }
      }
    },
    series: [
      {
        name: '桌面端',
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 3,
          color: '#8b5cf6'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(139, 92, 246, 0.3)' },
              { offset: 1, color: 'rgba(139, 92, 246, 0.05)' }
            ]
          }
        },
        data: [2400, 2800, 3200, 2900, 3100, 3400, 3600, 3300, 3500, 3800, 4000, 3700, 3900]
      },
      {
        name: '移动端',
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 3,
          color: '#10b981'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
              { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
            ]
          }
        },
        data: [1800, 2100, 2400, 2200, 2300, 2600, 2800, 2500, 2700, 3000, 3200, 2900, 3100]
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>

<style scoped>
.dashboard {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 32px;
  width: 100%;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #10b981;
}

.stat-trend.negative {
  color: #ef4444;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.stat-subtitle {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: #6b7280;
}

.chart-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  margin-bottom: 32px;
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.chart-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.chart-title p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.table-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  width: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.table-tabs {
  display: flex;
  gap: 8px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.data-table {
  border: none;
}

.data-table :deep(.el-table__header) {
  background-color: #f9fafb;
}

.data-table :deep(.el-table th) {
  border: none;
  background-color: #f9fafb;
  color: #6b7280;
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table :deep(.el-table td) {
  border: none;
  color: #1f2937;
}

.data-table :deep(.el-table__row:hover > td) {
  background-color: #f9fafb;
}
</style>
