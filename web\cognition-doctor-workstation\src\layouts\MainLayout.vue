<template>
  <div class="main-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar">
        <div class="logo-section">
          <div class="logo">
            <el-icon size="24"><Monitor /></el-icon>
            <span v-if="!isCollapsed" class="logo-text">认知测试系统</span>
          </div>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapsed"
          :collapse-transition="false"
          router
        >
          <el-menu-item index="/dashboard" class="menu-item">
            <el-icon><Odometer /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>

          <el-menu-item index="/patients" class="menu-item">
            <el-icon><Refresh /></el-icon>
            <template #title>患者管理</template>
          </el-menu-item>

          <el-menu-item index="/analytics" class="menu-item">
            <el-icon><TrendCharts /></el-icon>
            <template #title>测试数据</template>
          </el-menu-item>

          <el-menu-item index="/projects" class="menu-item">
            <el-icon><Folder /></el-icon>
            <template #title>报告中心</template>
          </el-menu-item>

          <el-menu-item index="/team" class="menu-item">
            <el-icon><User /></el-icon>
            <template #title>用户管理</template>
          </el-menu-item>
          
          <el-divider class="menu-divider" />
          
          <div class="menu-section-title" v-if="!isCollapsed">系统功能</div>

          <el-menu-item index="/data-library" class="menu-item">
            <el-icon><DataBoard /></el-icon>
            <template #title>数据统计</template>
          </el-menu-item>

          <el-menu-item index="/reports" class="menu-item">
            <el-icon><Document /></el-icon>
            <template #title>评分管理</template>
          </el-menu-item>

          <el-menu-item index="/workstation" class="menu-item">
            <el-icon><Monitor /></el-icon>
            <template #title>医生工作站</template>
          </el-menu-item>

          <el-menu-item index="/more" class="menu-item">
            <el-icon><MoreFilled /></el-icon>
            <template #title>更多功能</template>
          </el-menu-item>
          
          <el-divider class="menu-divider" />
          
          <el-menu-item index="/settings" class="menu-item">
            <el-icon><Setting /></el-icon>
            <template #title>系统设置</template>
          </el-menu-item>

          <el-menu-item index="/get-help" class="menu-item">
            <el-icon><QuestionFilled /></el-icon>
            <template #title>帮助中心</template>
          </el-menu-item>

          <el-menu-item index="/search" class="menu-item">
            <el-icon><Search /></el-icon>
            <template #title>搜索</template>
          </el-menu-item>
        </el-menu>
        
        <!-- 用户信息 -->
        <div class="user-section" v-if="!isCollapsed">
          <div class="user-info">
            <el-avatar :size="32" class="user-avatar">
              {{ authStore.user?.name?.charAt(0) }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ authStore.user?.name }}</div>
              <div class="user-email">{{ authStore.user?.email }}</div>
            </div>
          </div>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部工具栏 -->
        <el-header class="header">
          <div class="header-left">
            <el-button 
              text 
              @click="toggleSidebar"
              class="collapse-btn"
            >
              <el-icon><Expand v-if="isCollapsed" /><Fold v-else /></el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-button text class="header-btn">
              <el-icon><Bell /></el-icon>
            </el-button>
            
            <el-dropdown @command="handleUserCommand">
              <el-button text class="user-dropdown">
                <el-avatar :size="32">
                  {{ authStore.user?.name?.charAt(0) }}
                </el-avatar>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor,
  Odometer,
  Refresh,
  TrendCharts,
  Folder,
  User,
  DataBoard,
  Document,
  MoreFilled,
  Setting,
  QuestionFilled,
  Search,
  Expand,
  Fold,
  Bell
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏状态
const isCollapsed = ref(false)
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '240px')

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 当前页面标题
const currentPageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/dashboard': '工作台',
    '/patients': '患者管理',
    '/analytics': '测试数据',
    '/projects': '报告中心',
    '/team': '用户管理',
    '/data-library': '数据统计',
    '/reports': '评分管理',
    '/workstation': '医生工作站',
    '/settings': '系统设置'
  }
  return titleMap[route.path] || '工作台'
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 处理用户下拉菜单
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.main-layout .el-container {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

.main-layout .el-aside {
  margin: 0;
  padding: 0;
}

.main-layout .el-header {
  margin: 0;
  padding: 0;
}

.main-layout .el-main {
  margin: 0;
  padding: 0;
}

.sidebar {
  background: white;
  border-right: 1px solid #e5e7eb;
  transition: width 0.3s ease;
  overflow: hidden;
  margin: 0;
  padding: 0;
  height: 100vh;
}

.logo-section {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 18px;
  color: #1f2937;
}

.logo-text {
  white-space: nowrap;
}

.sidebar-menu {
  border: none;
  padding: 16px 0;
}

.menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  height: 40px;
}

.menu-item:hover {
  background-color: #f3f4f6;
}

.menu-item.is-active {
  background-color: #3b82f6;
  color: white;
}

.menu-section-title {
  padding: 16px 24px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.menu-divider {
  margin: 16px 12px;
  border-color: #e5e7eb;
}

.user-section {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  padding: 0 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f9fafb;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  margin: 0;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  padding: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  padding: 8px;
}

.user-dropdown {
  padding: 4px;
}

.main-content {
  background-color: #f9fafb;
  padding: 24px;
  margin: 0;
  width: 100%;
  height: calc(100vh - 64px);
  overflow-y: auto;
}
</style>
